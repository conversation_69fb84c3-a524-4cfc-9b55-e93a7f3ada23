"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Textarea } from "@workspace/ui/components/textarea";
import { Switch } from "@workspace/ui/components/switch";
import { Badge } from "@workspace/ui/components/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { Checkbox } from "@workspace/ui/components/checkbox";
import {
  Plus,
  X,
  Loader2,
  Building2,
  <PERSON>r,
  Phone,
  Mail,
  MapPin,
  FileText,
} from "lucide-react";
import { toast } from "sonner";
import { usePartnerActions, type Partner } from "@/hooks/usePartners";

// 合作类型选项
const COOPERATION_TYPES = [
  { value: "product_supply", label: "产品供应" },
  { value: "service_provider", label: "服务提供" },
  { value: "technology_partner", label: "技术合作" },
  { value: "marketing_partner", label: "营销合作" },
  { value: "distribution", label: "分销合作" },
  { value: "strategic_alliance", label: "战略联盟" },
  { value: "joint_venture", label: "合资企业" },
  { value: "other", label: "其他" },
];

// 表单验证 schema
const partnerFormSchema = z.object({
  name: z.string().min(1, "合作方名称不能为空"),
  code: z.string().min(1, "合作方代码不能为空"),
  logo: z.string().optional(),
  contactName: z.string().min(1, "联系人姓名不能为空"),
  contactPhone: z.string().min(1, "联系电话不能为空"),
  contactEmail: z.string().email("请输入有效的邮箱地址"),
  address: z.string().optional(),
  cooperationType: z.array(z.string()).min(1, "请选择至少一种合作类型"),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

type PartnerFormData = z.infer<typeof partnerFormSchema>;

interface PartnerFormProps {
  mode: "create" | "edit";
  initialData?: Partial<Partner>;
  onSuccess?: (partner: Partner) => void;
  onCancel?: () => void;
}

export function PartnerForm({
  mode,
  initialData,
  onSuccess,
  onCancel,
}: PartnerFormProps) {
  const router = useRouter();
  const { createPartner, updatePartner, loading } = usePartnerActions();

  const form = useForm<PartnerFormData>({
    resolver: zodResolver(partnerFormSchema),
    defaultValues: {
      name: "",
      code: "",
      logo: "",
      contactName: "",
      contactPhone: "",
      contactEmail: "",
      address: "",
      cooperationType: [],
      description: "",
      isActive: true,
      ...initialData,
    },
  });

  // 提交表单
  const onSubmit = async (data: PartnerFormData) => {
    try {
      let result;
      if (mode === "create") {
        result = await createPartner(data);
      } else {
        result = await updatePartner(initialData?.id!, data);
      }

      if (result.success) {
        toast.success(mode === "create" ? "合作方创建成功！" : "合作方更新成功！");
        if (onSuccess) {
          onSuccess(result.data!);
        } else {
          router.push("/products/partners");
        }
      } else {
        toast.error(result.message || "操作失败");
      }
    } catch (error) {
      console.error("保存失败:", error);
      toast.error("保存失败，请重试");
    }
  };

  // 处理合作类型选择
  const handleCooperationTypeChange = (type: string, checked: boolean) => {
    const currentTypes = form.getValues("cooperationType");
    if (checked) {
      form.setValue("cooperationType", [...currentTypes, type]);
    } else {
      form.setValue("cooperationType", currentTypes.filter(t => t !== type));
    }
  };

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* 基本信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building2 className="mr-2 h-5 w-5" />
                基本信息
              </CardTitle>
              <CardDescription>
                填写合作方的基本信息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>合作方名称 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入合作方名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>合作方代码 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入合作方代码" {...field} />
                      </FormControl>
                      <FormDescription>
                        用于系统内部标识，必须唯一
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="logo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Logo URL</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入Logo图片URL（可选）" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>描述</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="请输入合作方描述（可选）"
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 联系信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                联系信息
              </CardTitle>
              <CardDescription>
                填写合作方的联系人信息
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="contactName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>联系人姓名 *</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入联系人姓名" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="contactPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系电话 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入联系电话" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>联系邮箱 *</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入联系邮箱" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>地址</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入地址（可选）" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 合作信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                合作信息
              </CardTitle>
              <CardDescription>
                选择合作类型和设置状态
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="cooperationType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>合作类型 *</FormLabel>
                    <FormDescription>
                      请选择至少一种合作类型
                    </FormDescription>
                    <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                      {COOPERATION_TYPES.map((type) => (
                        <div key={type.value} className="flex items-center space-x-2">
                          <Checkbox
                            id={type.value}
                            checked={field.value.includes(type.value)}
                            onCheckedChange={(checked) =>
                              handleCooperationTypeChange(type.value, checked as boolean)
                            }
                          />
                          <label
                            htmlFor={type.value}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {type.label}
                          </label>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">启用状态</FormLabel>
                      <FormDescription>
                        是否启用此合作方
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                if (onCancel) {
                  onCancel();
                } else {
                  router.push("/products/partners");
                }
              }}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {mode === "create" ? "创建合作方" : "更新合作方"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

export default PartnerForm;
