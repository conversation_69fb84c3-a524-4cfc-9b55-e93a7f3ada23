"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Textarea } from "@workspace/ui/components/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Separator } from "@workspace/ui/components/separator";
import { Badge } from "@workspace/ui/components/badge";
import {
  ArrowLeft,
  Save,
  Plus,
  X,
  Upload,
  Calendar,
  MapPin,
  Users,
  DollarSign,
  FileText,
  Loader2,
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { useProductActions, useProductCategories } from "@/hooks/useProducts";
import { usePartnersList } from "@/hooks/usePartners";
import { ProductType, ProductStatus } from "@workspace/database";
import Link from "next/link";
import { toast } from "sonner";
import { PERMISSIONS } from "@/config/permissions.config";
import { FileUpload } from "@/components/upload/file-upload";

// 产品类型选项
const PRODUCT_TYPES = [
  { value: "STUDY_CAMP", label: "研学营" },
  { value: "CERTIFICATE", label: "证书" },
  { value: "BACKGROUND", label: "背景提升" },
  { value: "SCHOOL_LINK", label: "名校链接" },
  { value: "INSTITUTION", label: "机构提升" },
  { value: "OTHER", label: "其他" },
];

// 产品状态选项
const PRODUCT_STATUS_OPTIONS = [
  { value: "DRAFT", label: "草稿" },
  { value: "ACTIVE", label: "上架" },
  { value: "INACTIVE", label: "下架" },
];

// 目标受众选项
const TARGET_AUDIENCE_OPTIONS = [
  "小学生",
  "初中生",
  "高中生",
  "大学生",
  "研究生",
  "职场人士",
  "家长",
  "教师",
];

export default function CreateProductPage() {
  const router = useRouter();
  const { hasPermission } = usePermissions();
  const { createProduct, loading: saving } = useProductActions();
  const { categories, loading: categoriesLoading } = useProductCategories();
  const { partners, loading: partnersLoading } = usePartnersList();

  // 权限检查
  const canCreate = hasPermission(PERMISSIONS.PRODUCT?.MANAGEMENT?.CREATE);

  // 表单状态
  const [formData, setFormData] = useState({
    // 基本信息
    name: "",
    code: "",
    type: "" as ProductType,
    categoryId: "",
    description: "",
    features: [] as string[],
    images: [] as string[],
    brochureUrl: "",

    // 价格信息
    price: "",
    priceUnit: "",
    priceNote: "",

    // 产品详情
    duration: "",
    location: "",
    startDate: "",
    endDate: "",
    capacity: "",
    minParticipants: "",

    // 适用对象
    targetAudience: [] as string[],
    ageRange: "",
    gradeRange: "",

    // 合作方
    partnerId: "",

    // 状态和优先级
    status: "DRAFT" as ProductStatus,
    priority: "0",

    // SEO
    metaTitle: "",
    metaDescription: "",
    metaKeywords: [] as string[],
  });

  // 临时输入状态
  const [featureInput, setFeatureInput] = useState("");
  const [imageInput, setImageInput] = useState("");
  const [keywordInput, setKeywordInput] = useState("");

  // 表单验证
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 权限检查
  if (!canCreate) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-destructive">权限不足</h1>
          <p className="text-muted-foreground mt-2">
            您没有创建产品的权限，请联系管理员
          </p>
          <Link href="/products">
            <Button className="mt-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回产品列表
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  // 表单字段更新
  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  // 添加功能特点
  const addFeature = () => {
    if (
      featureInput.trim() &&
      !formData.features.includes(featureInput.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        features: [...prev.features, featureInput.trim()],
      }));
      setFeatureInput("");
    }
  };

  // 删除功能特点
  const removeFeature = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index),
    }));
  };

  // 添加图片
  const addImage = () => {
    if (imageInput.trim() && !formData.images.includes(imageInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        images: [...prev.images, imageInput.trim()],
      }));
      setImageInput("");
    }
  };

  // 删除图片
  const removeImage = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index),
    }));
  };

  // 添加关键词
  const addKeyword = () => {
    if (
      keywordInput.trim() &&
      !formData.metaKeywords.includes(keywordInput.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        metaKeywords: [...prev.metaKeywords, keywordInput.trim()],
      }));
      setKeywordInput("");
    }
  };

  // 删除关键词
  const removeKeyword = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      metaKeywords: prev.metaKeywords.filter((_, i) => i !== index),
    }));
  };

  // 切换目标受众
  const toggleTargetAudience = (audience: string) => {
    setFormData((prev) => ({
      ...prev,
      targetAudience: prev.targetAudience.includes(audience)
        ? prev.targetAudience.filter((a) => a !== audience)
        : [...prev.targetAudience, audience],
    }));
  };

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = "产品名称不能为空";
    }

    if (!formData.type) {
      newErrors.type = "请选择产品类型";
    }

    if (!formData.categoryId) {
      newErrors.categoryId = "请选择产品分类";
    }

    if (!formData.description.trim()) {
      newErrors.description = "产品描述不能为空";
    }

    if (formData.price && isNaN(Number(formData.price))) {
      newErrors.price = "价格必须是数字";
    }

    if (formData.capacity && isNaN(Number(formData.capacity))) {
      newErrors.capacity = "容量必须是数字";
    }

    if (formData.minParticipants && isNaN(Number(formData.minParticipants))) {
      newErrors.minParticipants = "最少参与人数必须是数字";
    }

    if (formData.priority && isNaN(Number(formData.priority))) {
      newErrors.priority = "优先级必须是数字";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("请检查表单中的错误信息");
      return;
    }

    try {
      const submitData = {
        ...formData,
        price: formData.price ? Number(formData.price) : undefined,
        capacity: formData.capacity ? Number(formData.capacity) : undefined,
        minParticipants: formData.minParticipants
          ? Number(formData.minParticipants)
          : undefined,
        priority: Number(formData.priority),
        startDate: formData.startDate || undefined,
        endDate: formData.endDate || undefined,
      };

      const result = await createProduct(submitData);

      if (result.success) {
        toast.success("产品创建成功");
        router.push("/products");
      } else {
        toast.error(result.message || "创建失败");
      }
    } catch (error) {
      toast.error("创建失败");
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/products">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-semibold tracking-tight">创建产品</h1>
            <p className="text-sm text-muted-foreground">
              填写产品信息并创建新产品
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-4 w-4" />
              基本信息
            </CardTitle>
            <CardDescription>
              产品的基本信息，包括名称、类型、分类等
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">
                  产品名称 <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="请输入产品名称"
                  className={errors.name ? "border-destructive" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-destructive">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="code">产品编码</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => handleInputChange("code", e.target.value)}
                  placeholder="自动生成或手动输入"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">
                  产品类型 <span className="text-destructive">*</span>
                </Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange("type", value)}
                >
                  <SelectTrigger
                    className={errors.type ? "border-destructive" : ""}
                  >
                    <SelectValue placeholder="选择产品类型" />
                  </SelectTrigger>
                  <SelectContent>
                    {PRODUCT_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.type && (
                  <p className="text-sm text-destructive">{errors.type}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="categoryId">
                  产品分类 <span className="text-destructive">*</span>
                </Label>
                <Select
                  value={formData.categoryId}
                  onValueChange={(value) =>
                    handleInputChange("categoryId", value)
                  }
                  disabled={categoriesLoading}
                >
                  <SelectTrigger
                    className={errors.categoryId ? "border-destructive" : ""}
                  >
                    <SelectValue placeholder="选择产品分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories
                      .filter(
                        (cat) => !formData.type || cat.type === formData.type,
                      )
                      .map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
                {errors.categoryId && (
                  <p className="text-sm text-destructive">
                    {errors.categoryId}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">
                产品描述 <span className="text-destructive">*</span>
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
                placeholder="请输入产品详细描述"
                rows={4}
                className={errors.description ? "border-destructive" : ""}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description}</p>
              )}
            </div>

            {/* 功能特点 */}
            <div className="space-y-2">
              <Label>产品特点</Label>
              <div className="flex space-x-2">
                <Input
                  value={featureInput}
                  onChange={(e) => setFeatureInput(e.target.value)}
                  placeholder="输入产品特点"
                  onKeyPress={(e) =>
                    e.key === "Enter" && (e.preventDefault(), addFeature())
                  }
                />
                <Button type="button" onClick={addFeature} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {formData.features.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.features.map((feature, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="pl-2 pr-1"
                    >
                      {feature}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="ml-1 h-auto p-0"
                        onClick={() => removeFeature(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 价格信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="mr-2 h-4 w-4" />
              价格信息
            </CardTitle>
            <CardDescription>产品的价格和计费方式</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="price">价格</Label>
                <Input
                  id="price"
                  value={formData.price}
                  onChange={(e) => handleInputChange("price", e.target.value)}
                  placeholder="0.00"
                  type="number"
                  step="0.01"
                  className={errors.price ? "border-destructive" : ""}
                />
                {errors.price && (
                  <p className="text-sm text-destructive">{errors.price}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="priceUnit">价格单位</Label>
                <Input
                  id="priceUnit"
                  value={formData.priceUnit}
                  onChange={(e) =>
                    handleInputChange("priceUnit", e.target.value)
                  }
                  placeholder="人/期/证书等"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="priceNote">价格说明</Label>
                <Input
                  id="priceNote"
                  value={formData.priceNote}
                  onChange={(e) =>
                    handleInputChange("priceNote", e.target.value)
                  }
                  placeholder="价格备注说明"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 产品详情 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-4 w-4" />
              产品详情
            </CardTitle>
            <CardDescription>产品的时间、地点、容量等详细信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="duration">时长</Label>
                <Input
                  id="duration"
                  value={formData.duration}
                  onChange={(e) =>
                    handleInputChange("duration", e.target.value)
                  }
                  placeholder="如：7天6夜"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">地点</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) =>
                    handleInputChange("location", e.target.value)
                  }
                  placeholder="活动地点"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="startDate">开始时间</Label>
                <Input
                  id="startDate"
                  type="datetime-local"
                  value={formData.startDate}
                  onChange={(e) =>
                    handleInputChange("startDate", e.target.value)
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">结束时间</Label>
                <Input
                  id="endDate"
                  type="datetime-local"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange("endDate", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="capacity">容量/名额</Label>
                <Input
                  id="capacity"
                  value={formData.capacity}
                  onChange={(e) =>
                    handleInputChange("capacity", e.target.value)
                  }
                  placeholder="最大容量"
                  type="number"
                  className={errors.capacity ? "border-destructive" : ""}
                />
                {errors.capacity && (
                  <p className="text-sm text-destructive">{errors.capacity}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="minParticipants">最少成团人数</Label>
                <Input
                  id="minParticipants"
                  value={formData.minParticipants}
                  onChange={(e) =>
                    handleInputChange("minParticipants", e.target.value)
                  }
                  placeholder="最少人数"
                  type="number"
                  className={errors.minParticipants ? "border-destructive" : ""}
                />
                {errors.minParticipants && (
                  <p className="text-sm text-destructive">
                    {errors.minParticipants}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 适用对象 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-4 w-4" />
              适用对象
            </CardTitle>
            <CardDescription>产品的目标受众和适用范围</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>目标受众</Label>
              <div className="flex flex-wrap gap-2">
                {TARGET_AUDIENCE_OPTIONS.map((audience) => (
                  <Badge
                    key={audience}
                    variant={
                      formData.targetAudience.includes(audience)
                        ? "default"
                        : "outline"
                    }
                    className="cursor-pointer"
                    onClick={() => toggleTargetAudience(audience)}
                  >
                    {audience}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ageRange">年龄范围</Label>
                <Input
                  id="ageRange"
                  value={formData.ageRange}
                  onChange={(e) =>
                    handleInputChange("ageRange", e.target.value)
                  }
                  placeholder="如：6-12岁"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gradeRange">年级范围</Label>
                <Input
                  id="gradeRange"
                  value={formData.gradeRange}
                  onChange={(e) =>
                    handleInputChange("gradeRange", e.target.value)
                  }
                  placeholder="如：小学1-6年级"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 合作方信息 */}
        <Card>
          <CardHeader>
            <CardTitle>合作方信息</CardTitle>
            <CardDescription>选择相关的合作方</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="partnerId">合作方</Label>
              <Select
                value={formData.partnerId}
                onValueChange={(value) => handleInputChange("partnerId", value)}
                disabled={partnersLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择合作方（可选）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="None">无合作方</SelectItem>
                  {partners.map((partner) => (
                    <SelectItem key={partner.id} value={partner.id}>
                      {partner.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* 图片和资源 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="mr-2 h-4 w-4" />
              图片和资源
            </CardTitle>
            <CardDescription>产品相关的图片和文档资源</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 产品图片 */}
            <div className="space-y-2">
              <Label>产品图片</Label>
              <div className="flex space-x-2">
                <Input
                  value={imageInput}
                  onChange={(e) => setImageInput(e.target.value)}
                  placeholder="输入图片URL"
                  onKeyPress={(e) =>
                    e.key === "Enter" && (e.preventDefault(), addImage())
                  }
                />
                <Button type="button" onClick={addImage} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {formData.images.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {formData.images.map((image, index) => (
                    <div key={index} className="relative">
                      <img
                        src={image}
                        alt={`产品图片 ${index + 1}`}
                        className="w-full h-24 object-cover rounded border"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-1 right-1 h-6 w-6 p-0"
                        onClick={() => removeImage(index)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* 产品手册 */}
            <div className="space-y-2">
              <Label htmlFor="brochureUrl">产品手册URL</Label>
              <Input
                id="brochureUrl"
                value={formData.brochureUrl}
                onChange={(e) =>
                  handleInputChange("brochureUrl", e.target.value)
                }
                placeholder="产品手册下载链接"
              />
            </div>
          </CardContent>
        </Card>

        {/* 状态和SEO */}
        <Card>
          <CardHeader>
            <CardTitle>状态和SEO</CardTitle>
            <CardDescription>产品的发布状态和搜索优化</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">产品状态</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) =>
                    handleInputChange("status", value as ProductStatus)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {PRODUCT_STATUS_OPTIONS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">优先级</Label>
                <Input
                  id="priority"
                  value={formData.priority}
                  onChange={(e) =>
                    handleInputChange("priority", e.target.value)
                  }
                  placeholder="0"
                  type="number"
                  className={errors.priority ? "border-destructive" : ""}
                />
                {errors.priority && (
                  <p className="text-sm text-destructive">{errors.priority}</p>
                )}
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="metaTitle">SEO标题</Label>
                <Input
                  id="metaTitle"
                  value={formData.metaTitle}
                  onChange={(e) =>
                    handleInputChange("metaTitle", e.target.value)
                  }
                  placeholder="搜索引擎显示的标题"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="metaDescription">SEO描述</Label>
                <Textarea
                  id="metaDescription"
                  value={formData.metaDescription}
                  onChange={(e) =>
                    handleInputChange("metaDescription", e.target.value)
                  }
                  placeholder="搜索引擎显示的描述"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>SEO关键词</Label>
                <div className="flex space-x-2">
                  <Input
                    value={keywordInput}
                    onChange={(e) => setKeywordInput(e.target.value)}
                    placeholder="输入关键词"
                    onKeyPress={(e) =>
                      e.key === "Enter" && (e.preventDefault(), addKeyword())
                    }
                  />
                  <Button type="button" onClick={addKeyword} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {formData.metaKeywords.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.metaKeywords.map((keyword, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="pl-2 pr-1"
                      >
                        {keyword}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="ml-1 h-auto p-0"
                          onClick={() => removeKeyword(index)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 提交按钮 */}
        <div className="flex justify-end space-x-4">
          <Link href="/products">
            <Button variant="outline" disabled={saving}>
              取消
            </Button>
          </Link>
          <Button type="submit" disabled={saving}>
            {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Save className="mr-2 h-4 w-4" />
            创建产品
          </Button>
        </div>
      </form>
    </div>
  );
}
